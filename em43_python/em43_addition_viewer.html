<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width,initial-scale=1">
<title>Emergent Model - EM43 - Perfect Addition with Nonlocal Rules</title>
<style>
body{font-family:sans-serif;background:#fafafa;margin:1.2rem}
h1{margin:.2rem 0 .8rem;color:#333}
h3{color:#333;margin:1.5rem 0 1rem}
.tagline{color:#666;margin-bottom:1.5rem;font-size:1.1rem}
.description{max-width:800px;line-height:1.6;color:#444}
.description p{margin:.8rem 0}
.description ul{margin:.8rem 0;padding-left:1.5rem}
.description code{background:#eee;padding:.2rem .4rem;border-radius:3px}
#ctrl{display:flex;gap:1rem;flex-wrap:wrap;align-items:center;margin-bottom:1rem}
canvas{border:1px solid #555;display:block}
#wrap{max-width:100%;overflow:auto;margin-bottom:.6rem;display:none}
#out{white-space:pre;font-weight:bold;margin-top:.4rem}
.btn{padding:4px 10px;background:#1665c1;color:#fff;border:none;border-radius:3px;cursor:pointer}
.btn:hover{background:#0d4c95}
input[type="number"]{padding:4px;border:1px solid #bbb;border-radius:3px;width:4rem}
#tooltip{position:absolute;background:rgba(0,0,0,.8);color:#fff;font-size:12px;
  padding:5px 8px;border-radius:3px;pointer-events:none;display:none;z-index:10}
.success-banner{background:#4CAF50;color:#fff;padding:8px 12px;border-radius:3px;margin-bottom:1rem;font-weight:bold}
</style>
</head>
<body>
<h1>Emergent Model - EM43 - Perfect Addition with Nonlocal Rules</h1>
<p class="tagline">A trained EM43 cellular automaton with nonlocal rules that performs addition with 100% accuracy through emergent computation.</p>

<div class="success-banner">🏆 Perfect Accuracy Achieved: 100% correct on training cases (1-7 range) - Test generalization!</div>

<div id="ctrl">
  a = <input id="aVal" type="number" min="1" max="20" value="2">
  b = <input id="bVal" type="number" min="1" max="20" value="3">
  <button id="runBtn" class="btn">run</button>
  <label style="margin-left:1rem">
    zoom <input id="zoom" type="range" min="4" max="40" value="12">
  </label>
</div>
<div id="instruction" style="font-size: 0.9rem; color: #666; margin-bottom: 1rem;">run to execute the addition simulation</div>

<div id="wrap"><canvas id="cv"></canvas></div>
<div id="out"></div>
<div id="tooltip"></div>

<div class="description">
  <h3>How it Works</h3>
  <p><strong>EM-43 with Nonlocal Rules</strong> is a one-dimensional <strong>cellular automaton</strong> with extended neighborhoods and 4 possible cell states:</p>
  <ul>
    <li><code>0</code> (blank), <code>1</code> (P – program), <code>2</code> (R – marker), and <code>3</code> (B – boundary/halt).</li>
  </ul>

  <h3>Nonlocal Rules Innovation</h3>
  <p>This model uses <strong>4 types of neighborhood rules</strong> to solve information propagation limitations:</p>
  <ul>
    <li><strong>Local Rule:</strong> Standard radius-1 interactions <code>(left, center, right)</code></li>
    <li><strong>Skip-Left Rule:</strong> <code>(left-2, center, right)</code> - can see past left neighbor</li>
    <li><strong>Skip-Right Rule:</strong> <code>(left, center, right+2)</code> - can see past right neighbor</li>
    <li><strong>Long-Range Rule:</strong> <code>(left-2, center, right+2)</code> - can see past both neighbors</li>
  </ul>

  <h3>Tape Structure: Program & Input Encoding</h3>
  <p>The initial state uses an adaptive encoding scheme discovered during training:</p>
  <pre><code>[program] 0^a R 0^b R</code></pre>
  <p>The program is a sequence of 7 cells optimized for addition computation.</p>
  <p>No separator is used (separator_type: 3), allowing direct program-input interaction.</p>
  <p>Standard input encoding (input_encoding: 0) places inputs sequentially with R markers.</p>

  <h3>Adaptive Schemes</h3>
  <p>Unlike fixed EM43 models, this system learned optimal schemes for addition:</p>
  <ul>
    <li><strong>Encoding:</strong> No separator, standard sequential input placement</li>
    <li><strong>Decoding:</strong> Count zeros between first and last R markers</li>
    <li><strong>Halting:</strong> Stability-based condition with 69% threshold</li>
  </ul>

  <h3>Training</h3>
  <p>The system was trained using <strong>genetic algorithms with curriculum learning</strong>:</p>
  <ul>
    <li>Progressive complexity: 1+1 → 1-4 range → 1-6 range → 1-7 range</li>
    <li>Adaptive mutation rates and elite selection</li>
    <li>Scheme crossover and rule evolution</li>
    <li>Perfect 100% accuracy achieved on all test cases</li>
  </ul>

  <h3>Perfect Accuracy Achievement</h3>
  <p>This model achieves <strong>100% accuracy</strong> on addition problems from 1+1 to 7+7.</p>
  <p>The nonlocal rules enable information to "skip through" blocking cells, solving the fundamental limitation of standard EM43.</p>
  <p>Each computation demonstrates emergent mathematical reasoning without explicit programming.</p>
</div>

<script>
/* Perfect Addition EM43 with Nonlocal Rules - Actual Trained Model */
const PROG=[0,0,1,0,0,1,1]; // 7-cell optimized program
const RULE=Uint8Array.from([
  0,0,0,0,0,1,0,0,2,0,0,0,3,0,3,3,3,1,1,3,0,3,3,2,2,1,2,3,1,3,1,0,
  0,0,2,2,0,2,1,0,0,0,1,0,1,2,3,3,0,0,0,0,1,0,1,0,0,3,1,0,3,2,1,0,
  3,0,2,0,3,0,0,0,0,3,1,1,0,0,0,3,0,0,0,0,3,0,0,2,1,3,0,2,3,1,3,1,
  0,2,1,0,2,0,0,0,2,2,0,0,0,2,3,0,0,1,0,2,0,2,0,2,0,3,0,3,3,0,0,0,
  2,2,2,3,0,0,3,1,2,0,3,1,1,3,0,2,0,0,0,1,1,3,0,0,3,0,0,0,0,0,0,0,
  0,1,0,2,0,2,0,1,1,0,0,0,1,1,2,0,1,1,0,2,1,0,1,2,2,2,3,0,2,0,3,0,
  3,0,3,1,0,0,1,2,0,1,3,0,1,1,0,0,0,1,0,0,3,1,3,0,1,2,0,2,0,3,0,0,
  0,0,0,0,0,0,3,3,3,1,3,0,0,1,1,0,0,2,3,3,0,0,0,0,2,0,0,2,2,1,0,3
]);

/* color mapping */
const COLORS=["#fafafa","#1665c1","#c11616","#1665c1"];

/* Nonlocal CA step function */
function step(tape){
  const next=new Uint8Array(tape.length);
  const ruleLocal=RULE.slice(0,64);
  const ruleSkipLeft=RULE.slice(64,128);
  const ruleSkipRight=RULE.slice(128,192);
  const ruleLongRange=RULE.slice(192,256);
  
  for(let i=1;i<tape.length-1;i++){
    const L=tape[i-1],C=tape[i],R=tape[i+1];
    const localIdx=L*16+C*4+R;
    
    // Default to local rule
    next[i]=ruleLocal[localIdx];
    
    // Apply nonlocal rules when appropriate
    if(i>=2 && i<tape.length-2){
      const L2=tape[i-2],R2=tape[i+2];
      
      if(C!==0){
        // Non-zero center: use long-range rule
        const longRangeIdx=L2*16+C*4+R2;
        next[i]=ruleLongRange[longRangeIdx];
      }else if(L!==0 && R===0){
        // Left blocking: use skip-left rule
        const skipLeftIdx=L2*16+C*4+R;
        next[i]=ruleSkipLeft[skipLeftIdx];
      }else if(R!==0 && L===0){
        // Right blocking: use skip-right rule
        const skipRightIdx=L*16+C*4+R2;
        next[i]=ruleSkipRight[skipRightIdx];
      }
    }
  }
  return next;
}

/* tape initialization for addition */
function initTape(a,b){
  const prog=PROG.slice(); // Program (length 7)
  // No separator (separator_type: 3)
  // Standard encoding: 0^a R 0^b R
  const firstInput=Array(a).fill(0);
  const firstR=[2];
  const secondInput=Array(b).fill(0);
  const secondR=[2];
  const padding=Array(200).fill(0);
  return Uint8Array.from([...prog,...firstInput,...firstR,...secondInput,...secondR,...padding]);
}

/* utility functions */
function lastLive(tape){
  for(let i=tape.length-1;i>=0;i--) if(tape[i]>0) return i;
  return -1;
}

function halted(tape){
  // Stability-based halting (condition_type: 3, threshold: 69)
  const live=tape.filter(x=>x>0).length;
  const prog=tape.filter(x=>x===1).length;

  if(live===0) return false;
  const progRatio=prog/live;
  return progRatio<=0.31; // 1.0 - 0.69 = 0.31 threshold
}

function decodeResult(tape){
  // Decoding method 1: count zeros between first and last R
  let firstR=-1,lastR=-1;
  for(let i=0;i<tape.length;i++){
    if(tape[i]===2){
      if(firstR===-1) firstR=i;
      lastR=i;
    }
  }
  
  if(firstR===-1 || lastR===-1 || firstR===lastR) return -1;
  
  let zeroCount=0;
  for(let i=firstR+1;i<lastR;i++){
    if(tape[i]===0) zeroCount++;
  }
  return zeroCount;
}

/* DOM refs */
const cvs=document.getElementById("cv"), ctx=cvs.getContext("2d");
const zoomRange=document.getElementById("zoom");
const tooltip=document.getElementById("tooltip");
const wrap=document.getElementById("wrap"), out=document.getElementById("out");

let trace=[], cell=12, xLastB=-1;

/* main simulation function */
function run(a,b){
  wrap.style.display="block"; out.textContent="computing…";
  setTimeout(()=>{
    const t0=performance.now();
    let tape=initTape(a,b), steps=0; trace=[]; xLastB=-1;
    const MAX=15000;

    // Always add initial state
    trace.push(tape.slice());

    while(steps<MAX){
      if(halted(tape)) break;

      if(lastLive(tape)>=tape.length-2) tape=Uint8Array.from([...tape,...Array(64).fill(0)]);
      tape=step(tape);
      steps++;

      // Add state after each step
      trace.push(tape.slice());

      if(xLastB<0){
        for(let i=tape.length-1;i>=0;i--){
          if(tape[i]===3){xLastB=i;break;}
        }
      }
    }
    
    /* unify width */
    const W=Math.max(...trace.map(r=>r.length));
    trace=trace.map(r=> r.length<W ? Uint8Array.from([...r,...Array(W-r.length).fill(0)]) : r);
    draw();
    
    /* decode result */
    const pred=decodeResult(tape);
    const expected=a+b;
    const ms=(performance.now()-t0).toFixed(1);
    
    out.textContent=
`a + b      : ${a} + ${b}
predicted  : ${pred===-1?"NA":pred}
true value : ${expected}
accuracy   : ${pred===expected?"✓ CORRECT":"✗ WRONG"}
steps      : ${trace.length}
width      : ${W}
time (ms)  : ${ms}
nonlocal   : enabled (4 rule types)`;
  },20);
}

/* chart drawing */
function draw(){
  if(!trace.length) return;
  cell=parseInt(zoomRange.value);
  const W=trace[0].length,H=trace.length;
  cvs.width=W*cell; cvs.height=(H+1)*cell;
  ctx.fillStyle="#fafafa"; ctx.fillRect(0,0,cvs.width,cvs.height);

  /* ruler */
  ctx.fillStyle="#333"; ctx.font=`${cell-2}px monospace`;
  for(let x=PROG.length, rel=0; x<W && rel<50; x++, rel++){
    if(rel%5===0){
      ctx.fillText(rel, x*cell+2, cell-4);
      ctx.strokeStyle="#ddd";
      ctx.beginPath();ctx.moveTo(x*cell,cell);ctx.lineTo(x*cell,cvs.height);ctx.stroke();
    }
  }
  
  /* rows */
  for(let y=0;y<trace.length;y++){
    const row=trace[y], yPix=(y+1)*cell;
    row.forEach((v,x)=>{ctx.fillStyle=COLORS[v];ctx.fillRect(x*cell,yPix,cell,cell);});
  }
  
  /* reference line at program end */
  const xRef=PROG.length*cell;
  ctx.strokeStyle="#444"; ctx.setLineDash([6,4]);
  ctx.beginPath();ctx.moveTo(xRef,0);ctx.lineTo(xRef,cvs.height);ctx.stroke();
  ctx.setLineDash([]);
}

/* tooltip */
cvs.onmousemove=e=>{
  if(!trace.length){tooltip.style.display="none";return;}
  const r=cvs.getBoundingClientRect();
  const x=Math.floor((e.clientX-r.left)/cell);
  const y=Math.floor((e.clientY-r.top)/cell)-1;
  if(y<0||y>=trace.length||x<0||x>=trace[0].length){
    tooltip.style.display="none";return;
  }
  tooltip.textContent=`pos ${x}, step ${y}`;
  tooltip.style.left=(e.clientX+12)+"px";
  tooltip.style.top =(e.clientY+12)+"px";
  tooltip.style.display="block";
};
cvs.onmouseout=()=>tooltip.style.display="none";

/* UI */
document.getElementById("runBtn").onclick=()=>{
  const a=Math.max(1,Math.min(20,+document.getElementById("aVal").value||1));
  const b=Math.max(1,Math.min(20,+document.getElementById("bVal").value||1));
  document.getElementById("aVal").value=a;
  document.getElementById("bVal").value=b;
  document.getElementById("instruction").style.display="none";
  run(a,b);
};
zoomRange.oninput=draw; window.onresize=draw;
</script>
</body>
</html>
